package com.jinghang.cash.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目信息DTO
 * 基于Flow系统的ProjectInfoVO改造
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
public class ProjectInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目唯一编码
     */
    @NotBlank(message = "项目编码不能为空")
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 资产方编码 (关联资产方表)
     */
    private String flowChannel;

    /**
     * 融担方编码 (关联融担方表)
     */
    private String guaranteeCode;

    /**
     * 资金方编码 (关联资金方表)
     */
    private String capitalChannel;

    /**
     * 项目类型编码 (关联项目类型表)
     */
    private String projectTypeCode;

    /**
     * 项目状态 (ENABLE/DISABLE)
     */
    private String enabled;

    /**
     * 项目开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    /**
     * 项目要素信息
     */
    private ProjectElementsDto elements;

    /**
     * 项目要素扩展信息
     */
    private ProjectElementsExtDto elementsExt;

    /**
     * 项目要素DTO
     */
    @Data
    public static class ProjectElementsDto implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 可提现范围(元) (格式 如 1000-50000)
         */
        private String drawableAmountRange;

        /**
         * 单笔提现步长(元)
         */
        private String drawableAmountStep;

        /**
         * 授信黑暗期 (格式 HH:mm-HH:mm)
         */
        private String creditDarkHours;

        /**
         * 用信黑暗期 (格式 HH:mm-HH:mm)
         */
        private String loanDarkHours;

        /**
         * 还款黑暗期 (格式 HH:mm-HH:mm)
         */
        private String repayDarkHours;

        /**
         * 资金方授信黑暗期
         */
        private String fundingCreditDarkHours;

        /**
         * 资金方用信黑暗期
         */
        private String fundingLoanDarkHours;

        /**
         * 资金方还款黑暗期
         */
        private String fundingRepayDarkHours;

        /**
         * 日授信限额(万元)
         */
        private BigDecimal dailyCreditLimit;

        /**
         * 日放款限额(万元)
         */
        private BigDecimal dailyLoanLimit;

        /**
         * 授信锁定期限(天)
         */
        private Integer creditLockDays;

        /**
         * 用信锁定期限(天)
         */
        private Integer loanLockDays;

        /**
         * 对客利率(%)
         */
        private String customerInterestRate;

        /**
         * 对资利率(%)
         */
        private String fundingInterestRate;

        /**
         * 年龄范围(岁) (格式 如 22-55)
         */
        private String ageRange;

        /**
         * 支持的还款类型 (英文逗号分隔)
         */
        private String supportedRepayTypes;

        /**
         * 借款期限 (英文逗号分隔)
         */
        private String loanTerms;

        /**
         * 资方路由
         */
        private String capitalRoute;

        /**
         * 项目时效类型（LONGTIME, TEMPORARY）
         */
        private String projectDurationType;

        /**
         * 临时配置有效期起
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime tempStartTime;

        /**
         * 临时配置有效期止
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime tempEndTime;

        /**
         * 启用状态
         */
        private String enabled;

        /**
         * 年结是否顺延
         */
        private String graceNext;
    }

    /**
     * 项目要素扩展DTO
     */
    @Data
    public static class ProjectElementsExtDto implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 年利率基数(天) (如 360或365)
         */
        private String interestDaysBasis;

        /**
         * 是否支持线下跨日还款
         */
        private String allowCrossDayRepay;

        /**
         * 风控模型渠道
         */
        private String riskModelChannel;

        /**
         * 放款支付渠道
         */
        private String loanPaymentChannel;

        /**
         * 扣款绑卡渠道
         */
        private String deductionBindCardChannel;

        /**
         * 扣款商户号
         */
        private String deductionMerchantCode;

        /**
         * 签章渠道
         */
        private String signChannel;

        /**
         * 逾期短信发送方
         */
        private String overdueSmsSender;

        /**
         * 短信渠道
         */
        private String smsChannel;

        /**
         * 逾期宽限期类型 (SQ:首期, MQ:每期)
         */
        private String gracePeriodType;

        /**
         * 逾期宽限期(天)
         */
        private String gracePeriodDays;

        /**
         * 节假日是否顺延
         */
        private String holidayPostpone;

        /**
         * 征信查询方
         */
        private String creditQueryParty;

        /**
         * 征信上报方
         */
        private String creditReportSender;

        /**
         * 催收方
         */
        private String collectionParty;

        /**
         * 是否推送催收数据
         */
        private String pushCollectionData;

        /**
         * 是否支持催收减免
         */
        private String allowCollectionWaiver;
    }

    /**
     * 判断项目是否启用
     */
    public boolean isEnabled() {
        return "ENABLE".equals(enabled);
    }

    /**
     * 判断项目是否在有效期内
     */
    public boolean isInValidPeriod() {
        LocalDate now = LocalDate.now();

        // 检查开始日期
        if (startDate != null && now.isBefore(startDate)) {
            return false;
        }

        // 检查结束日期
        if (endDate != null && now.isAfter(endDate)) {
            return false;
        }

        return true;
    }

    /**
     * 判断项目是否有效（启用且在有效期内）
     */
    public boolean isValid() {
        return isEnabled() && isInValidPeriod();
    }
}
