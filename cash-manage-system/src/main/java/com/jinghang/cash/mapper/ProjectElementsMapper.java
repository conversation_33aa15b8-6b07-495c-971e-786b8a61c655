package com.jinghang.cash.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.enums.AbleStatus;
import com.jinghang.cash.pojo.project.ProjectElements;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目要素Mapper
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Mapper
public interface ProjectElementsMapper extends BaseMapper<ProjectElements> {

    /**
     * 根据项目编码查询项目要素
     *
     * @param projectCode 项目编码
     * @return 项目要素
     */
    ProjectElements selectByProjectCode(@Param("projectCode") String projectCode);

    /**
     * 根据项目编码查询启用状态的项目要素，按时效类型排序（临时优先）
     *
     * @param projectCode 项目编码
     * @param enabled 启用状态
     * @return 项目要素列表
     */
    List<ProjectElements> selectByProjectCodeAndEnabledOrderByProjectDurationTypeDesc(
            @Param("projectCode") String projectCode, 
            @Param("enabled") AbleStatus enabled);

    /**
     * 根据项目编码查询有效的临时项目要素
     *
     * @param projectCode 项目编码
     * @param enabled 启用状态
     * @param durationType 时效类型
     * @param currentTime 当前时间
     * @return 项目要素
     */
    ProjectElements selectValidTemporaryElements(
            @Param("projectCode") String projectCode,
            @Param("enabled") AbleStatus enabled,
            @Param("durationType") String durationType,
            @Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据项目编码查询长期项目要素
     *
     * @param projectCode 项目编码
     * @param enabled 启用状态
     * @param durationType 时效类型
     * @return 项目要素
     */
    ProjectElements selectByProjectCodeAndEnabledAndProjectDurationType(
            @Param("projectCode") String projectCode,
            @Param("enabled") AbleStatus enabled,
            @Param("durationType") String durationType);
}
