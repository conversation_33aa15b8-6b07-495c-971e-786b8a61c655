package com.jinghang.cash.modules.manage.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.annotation.Log;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.service.DrainageStatisticsDataService;
import com.jinghang.cash.modules.manage.vo.req.DrainageStatisticsRequest;
import com.jinghang.cash.pojo.DrainageStatistics;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> gale
 * @Classname DrainageStatisticsController
 * @Description 引流统计
 * @Date 2023/11/20 16:50
 */
@RestController
@RequestMapping("/projectInfo")
public class ProjectInfoController {

}
