package com.jinghang.cash.service;

import com.jinghang.cash.api.vo.ProjectInfoVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 项目信息服务测试
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@SpringBootTest
@ActiveProfiles("test")
public class ProjectInfoServiceTest {

    @Autowired
    private ProjectInfoService projectInfoService;

    @Test
    public void testQueryProjectInfo() {
        // 测试空参数
        ProjectInfoVO result1 = projectInfoService.queryProjectInfo(null);
        assertNull(result1);

        ProjectInfoVO result2 = projectInfoService.queryProjectInfo("");
        assertNull(result2);

        ProjectInfoVO result3 = projectInfoService.queryProjectInfo("   ");
        assertNull(result3);

        // 测试不存在的项目编码
        ProjectInfoVO result4 = projectInfoService.queryProjectInfo("NOT_EXIST");
        assertNull(result4);

        // 注意：实际的项目编码测试需要在有数据的环境中进行
        // ProjectInfoDto result5 = projectInfoService.queryProjectInfo("TEST001");
        // assertNotNull(result5);
        // assertEquals("TEST001", result5.getProjectCode());
    }

    @Test
    public void testClearProjectInfoCache() {
        // 测试清除缓存
        assertDoesNotThrow(() -> {
            projectInfoService.clearProjectInfoCache("TEST001");
        });

        // 测试空参数
        assertDoesNotThrow(() -> {
            projectInfoService.clearProjectInfoCache(null);
            projectInfoService.clearProjectInfoCache("");
        });
    }
}
